"""
人员入侵检测后处理器
符合智驱力标准接口
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(__file__)
sys.path.insert(0, current_dir)

# 导入基类 - 使用绝对路径导入
postprocessor_file = os.path.join(current_dir, 'postprocessor.py')
import importlib.util
spec = importlib.util.spec_from_file_location("postprocessor_module", postprocessor_file)
postprocessor_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(postprocessor_module)
BasePostprocessor = postprocessor_module.Postprocessor


class Postprocessor(BasePostprocessor):
    """人员入侵检测后处理器"""

    def __init__(self, source_id, alg_name):
        super().__init__(source_id, alg_name)

    def _process(self, result, filter_result, config_data=None):
        """处理检测结果 - 参考车辆计数逻辑"""
        # 保存配置数据到实例变量
        self.config_data = config_data

        # 首先进行置信度过滤
        from logger import LOGGER
        from datetime import datetime

        LOGGER.info(f"开始人员区域入侵后处理，配置数据: {list(config_data.keys()) if config_data else 'None'}")
        if config_data:
            model_params = config_data.get('model_parameters', {})
            LOGGER.info(f"模型参数: {model_params}")
            confidence_threshold = model_params.get('confidence_threshold', 0.5)
            LOGGER.info(f"读取到的置信度阈值: {confidence_threshold}")

        # 调用过滤方法
        filtered_result = self._filter_detections(result)

        hit = False
        # 获取策略
        if self.strategy is None:
            self.strategy = self.reserved_args.get('strategy', 'center')

        # 生成多边形区域（使用配置数据）
        polygons = self._gen_polygons(config_data)

        try:
            # 获取过滤后的检测结果
            model_name, rectangles = next(iter(filtered_result.items()))
            LOGGER.info(f"人员区域入侵处理: 模型={model_name}, 检测框数量={len(rectangles)}")

            # 处理每个检测框
            processed_rectangles = []
            intrusion_count = 0  # 入侵计数

            for rectangle in rectangles:
                is_alert = False
                alert_reason = ""

                # 检查是否在任何多边形区域内
                for area_id, area in polygons.items():
                    if self._is_rectangle_in_polygon(rectangle['xyxy'], area['polygon'], self.strategy):
                        is_alert = True
                        intrusion_count += 1
                        alert_reason = f"人员入侵{area['name']}"
                        LOGGER.info(f'人员入侵检测区域: area={area_id}')
                        break

                # 设置颜色和告警状态
                if is_alert:
                    hit = True
                    rectangle['color'] = self.alert_color
                    rectangle['alert_reason'] = alert_reason
                    LOGGER.info(f"检测到人员入侵: {alert_reason}")
                else:
                    rectangle['color'] = self.non_alert_color

                processed_rectangles.append(rectangle.copy())

            # 判断是否触发告警（根据告警阈值）
            alert_threshold = 1
            if hasattr(self, 'config_data') and self.config_data:
                alert_params = self.config_data.get('alert_parameters', {})
                alert_threshold = alert_params.get('alert_threshold', 1)

            hit = intrusion_count >= alert_threshold

            if hit:
                message = f"人员区域入侵告警: 检测到{intrusion_count}个人员入侵 (阈值: {alert_threshold})"
            else:
                message = f"检测到 {len(processed_rectangles)} 个人员目标，入侵: {intrusion_count} (阈值: {alert_threshold})"

            LOGGER.info(f"人员区域入侵处理完成: hit={hit}, message={message}")

            # 构建返回结果，使用自定义格式
            return {
                'hit': hit,
                'message': message,
                'details': {
                    'detections': processed_rectangles,
                    'polygons': polygons,
                    'intrusion_count': intrusion_count,
                    'alert_threshold': alert_threshold,
                    'frame_shape': [640, 480],  # 默认尺寸，实际会从frame获取
                    'timestamp': datetime.now().isoformat()
                }
            }

        except Exception as e:
            LOGGER.error(f'人员区域入侵处理失败: {e}')
            return {
                'hit': False,
                'message': f'处理失败: {str(e)}',
                'details': {}
            }

    def _filter(self, model_name, model_data):
        """过滤检测结果"""
        targets = []
        model_conf = model_data['model_conf']
        engine_result = model_data['engine_result']

        for engine_result_ in engine_result:
            # 过滤掉置信度低于阈值的目标
            if not self._filter_by_conf(model_conf, engine_result_['conf']):
                continue

            # 过滤掉不在label列表中的目标
            label = self._filter_by_label(model_conf, engine_result_['label'])
            if not label:
                continue

            # 坐标缩放
            xyxy = self._scale(engine_result_['xyxy'])

            # 生成矩形框
            targets.append(self._gen_rectangle(xyxy, self.non_alert_color, label, engine_result_['conf']))

        return targets

    def _gen_polygons(self, config_data=None):
        """生成多边形区域"""
        polygons = {}

        if config_data:
            # 获取图像尺寸
            image_width = config_data.get('image_width', 640)
            image_height = config_data.get('image_height', 480)

            # 从配置数据中获取bbox配置
            bbox_config = config_data.get('bbox_config', {})
            polygon_configs = bbox_config.get('polygons', [])

            from logger import LOGGER
            LOGGER.info(f"从配置中读取到 {len(polygon_configs)} 个多边形区域")

            for i, polygon_config in enumerate(polygon_configs):
                polygon_id = f"polygon_{i}"

                # 获取多边形点坐标
                points = polygon_config.get('points', [])
                if len(points) >= 3:  # 至少需要3个点构成多边形
                    # 转换相对坐标为绝对坐标
                    absolute_points = []
                    for point in points:
                        if isinstance(point, list) and len(point) >= 2:
                            # 如果坐标值在0-1之间，认为是相对坐标
                            if 0 <= point[0] <= 1 and 0 <= point[1] <= 1:
                                abs_x = int(point[0] * image_width)
                                abs_y = int(point[1] * image_height)
                            else:
                                abs_x = int(point[0])
                                abs_y = int(point[1])
                            absolute_points.append([abs_x, abs_y])

                    if len(absolute_points) >= 3:
                        polygons[polygon_id] = {
                            'polygon': absolute_points,
                            'name': polygon_config.get('name', f'入侵区域{i+1}'),
                            'color': polygon_config.get('color', [255, 0, 0]),
                            'thickness': polygon_config.get('thickness', 2)
                        }

            LOGGER.info(f"总共生成 {len(polygons)} 个入侵检测区域")
        return polygons

    def _filter_detections(self, result: dict) -> dict:
        """过滤检测结果，支持置信度过滤"""
        # 默认置信度阈值
        confidence_threshold = 0.5
        nms_threshold = 0.5

        # 从配置数据中获取阈值
        if hasattr(self, 'config_data') and self.config_data:
            model_params = self.config_data.get('model_parameters', {})
            confidence_threshold = model_params.get('confidence_threshold', confidence_threshold)
            nms_threshold = model_params.get('nms_threshold', nms_threshold)

        from logger import LOGGER
        LOGGER.info(f"使用置信度阈值: {confidence_threshold}, NMS阈值: {nms_threshold}")

        filtered_result = {}

        for model_name, detections in result.items():
            filtered_detections = []

            for detection in detections:
                # 置信度过滤
                confidence = detection.get('conf', detection.get('confidence', 0))
                if confidence >= confidence_threshold:
                    filtered_detections.append(detection)

            filtered_result[model_name] = filtered_detections
            LOGGER.info(f"模型 {model_name}: 原始检测数={len(detections)}, 过滤后={len(filtered_detections)}")

        return filtered_result


