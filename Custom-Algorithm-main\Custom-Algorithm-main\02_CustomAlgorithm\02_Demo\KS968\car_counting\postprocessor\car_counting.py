from logger import LOGGER
import sys
import os
from typing import Dict
sys.path.insert(0, os.path.dirname(__file__))
from postprocessor import Postprocessor as BasePostprocessor
from tracker import Tracker
from datetime import datetime


class Postprocessor(BasePostprocessor):
    def __init__(self, source_id, alg_name):
        super().__init__(source_id, alg_name)
        self.strategy = None
        self.tracker = None
        self.max_retain = 0
        self.targets = {}
        self.lines = None
        self.reserved_args = {
            'strategy': 'counting',
            'frame_interval': 1
        }
        self.frame_interval = 1
        self.alert_color = [255, 0, 0]  # 红色
        self.non_alert_color = [0, 255, 0]  # 绿色

    def _gen_polygons(self, config_data=None):
        """生成多边形区域"""
        polygons = {}

        if config_data:
            # 获取图像尺寸
            image_width = config_data.get('image_width', 640)
            image_height = config_data.get('image_height', 480)
            LOGGER.info(f"生成多边形区域，图像尺寸: {image_width}x{image_height}")
            
            # 从数据库配置中读取检测区域
            detection_areas = config_data.get('detection_areas', [])
            polygons_config = config_data.get('polygons', [])

            # 处理detection_areas格式
            for i, area in enumerate(detection_areas):
                area_id = area.get('id', f'area_{i}')
                points = area.get('points', [])
                if points:
                    # 转换坐标格式
                    polygon_points = []
                    for point in points:
                        if isinstance(point, dict) and 'x' in point and 'y' in point:
                            # 相对坐标转绝对坐标
                            x = int(point['x'] * image_width)
                            y = int(point['y'] * image_height)
                            polygon_points.append([x, y])
                            LOGGER.debug(f"区域{area_id} 点转换: 相对({point['x']:.3f}, {point['y']:.3f}) -> 绝对({x}, {y})")
                        elif isinstance(point, (list, tuple)) and len(point) >= 2:
                            polygon_points.append([int(point[0]), int(point[1])])

                    if polygon_points:
                        polygons[area_id] = {
                            'polygon': polygon_points,
                            'name': area.get('name', f'区域{i+1}')
                        }
                        LOGGER.info(f"生成区域{area_id}: {len(polygon_points)}个点")

            # 处理polygons格式（兼容旧格式）
            for i, polygon in enumerate(polygons_config):
                if isinstance(polygon, dict) and 'points' in polygon:
                    area_id = polygon.get('id', f'polygon_{i}')
                    points = polygon['points']
                    polygon_points = []
                    for point in points:
                        if isinstance(point, dict) and 'x' in point and 'y' in point:
                            x = int(point['x'] * image_width)
                            y = int(point['y'] * image_height)
                            polygon_points.append([x, y])

                    if polygon_points:
                        polygons[area_id] = {
                            'polygon': polygon_points,
                            'name': polygon.get('name', f'多边形{i+1}')
                        }

        LOGGER.info(f"总共生成 {len(polygons)} 个检测区域")
        return polygons

    def _gen_lines(self, config_data=None):
        """生成计数线"""
        lines = {}
        if config_data:
            # 获取图像尺寸
            image_width = config_data.get('image_width', 640)
            image_height = config_data.get('image_height', 480)
            LOGGER.info(f"生成计数线，图像尺寸: {image_width}x{image_height}")
            
            # 从数据库配置中读取计数线
            detection_lines = config_data.get('detection_lines', [])
            # 处理detection_lines格式
            for i, line in enumerate(detection_lines):
                line_id = line.get('id', f'line_{i}')
                points = line.get('points', [])
                if len(points) >= 2:
                    # 转换坐标格式
                    line_points = []
                    for point in points:
                        if isinstance(point, dict) and 'x' in point and 'y' in point:
                            # 相对坐标转绝对坐标
                            x = int(point['x'] * image_width)
                            y = int(point['y'] * image_height)
                            line_points.append([x, y])
                        elif isinstance(point, (list, tuple)) and len(point) >= 2:
                            line_points.append([int(point[0]), int(point[1])])

                    if len(line_points) >= 2:
                        lines[line_id] = {
                            'line': line_points,
                            'ext': {
                                'direction': line.get('direction', 'both'),
                                'name': line.get('name', f'计数线{i+1}'),
                                'result': {
                                    'up': 0,
                                    'down': 0,
                                    'left': 0,
                                    'right': 0,
                                    'total': 0
                                }
                            }
                        }
                        LOGGER.info(f"生成计数线{line_id}: {len(line_points)}个点")
        
        LOGGER.info(f"总共生成 {len(lines)} 条计数线")
        return lines

    def _point_in_polygon(self, point, polygon):
        """判断点是否在多边形内（射线法）"""
        x, y = point
        n = len(polygon)
        inside = False
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        return inside

    def _cross_line_counting(self, pre_bbox, cur_bbox, line, direction, strategy):
        """跨线计数逻辑"""
        # 获取目标中心点
        pre_center = [(pre_bbox[0] + pre_bbox[2]) / 2, (pre_bbox[1] + pre_bbox[3]) / 2]
        cur_center = [(cur_bbox[0] + cur_bbox[2]) / 2, (cur_bbox[1] + cur_bbox[3]) / 2]
        # 检查是否跨越计数线
        line_y = line[0][1] 
        if pre_center[1] < line_y and cur_center[1] > line_y:
            # 从上往下跨线
            return {'direction': 'down', 'count': 1}
        elif pre_center[1] > line_y and cur_center[1] < line_y:
            # 从下往上跨线
            return {'direction': 'up', 'count': 1}
        return None

    def _merge_cross_line_counting_result(self, line_result, count_result):
        """合并跨线计数结果"""
        direction = count_result['direction']
        count = count_result['count']
        if direction in line_result:
            line_result[direction] += count
        # 更新总计数
        line_result['total'] = line_result.get('up', 0) + line_result.get('down', 0)

    def __check_lost_target(self, tracker_result):
        for track_id in list(self.targets.keys()):
            if track_id not in tracker_result:
                self.targets[track_id]['lost'] += 1
            else:
                self.targets[track_id]['lost'] = 0
            if self.targets[track_id]['lost'] > self.max_retain:
                LOGGER.info('Target lost, source_id={}, alg_name={}, track_id={}, pre_target={}'.format(
                    self.source_id, self.alg_name, track_id, self.targets[track_id]['pre_target']))
                del self.targets[track_id]
        return True

    def _filter_detections(self, result: Dict) -> Dict:
        """过滤检测结果，支持置信度过滤"""
        # 默认置信度阈值
        confidence_threshold = 0.5
        nms_threshold = 0.5
        
        # 从config_data中读取参数
        if hasattr(self, 'config_data') and self.config_data:
            # 支持从model_parameters中读取参数
            model_params = self.config_data.get('model_parameters', {})
            confidence_threshold = model_params.get('confidence_threshold', confidence_threshold)
            nms_threshold = model_params.get('nms_threshold', nms_threshold)
            
            # 也支持直接从config_data根级别读取
            if 'confidence_threshold' in self.config_data:
                confidence_threshold = self.config_data['confidence_threshold']
            if 'nms_threshold' in self.config_data:
                nms_threshold = self.config_data['nms_threshold']
        
        # 兼容result结构，过滤低置信度的检测结果
        filtered = {}
        for k, v in result.items():
            if isinstance(v, list):
                filtered[k] = []
                for det in v:
                    # 支持多种置信度字段名
                    confidence = det.get('conf', det.get('confidence', 0))
                    if confidence >= confidence_threshold:
                        filtered[k].append(det)
            else:
                filtered[k] = v
        
        LOGGER.info(f"置信度过滤: 阈值={confidence_threshold}, NMS阈值={nms_threshold}, 过滤前={len(result.get('detections', []))}, 过滤后={len(filtered.get('detections', []))}")
        return filtered

    def _process(self, result, filter_result, config_data=None):
        """处理检测结果"""
        # 保存配置数据到实例变量，供_filter_detections使用
        self.config_data = config_data
        
        # 首先进行置信度过滤
        LOGGER.info(f"开始后处理，配置数据: {list(config_data.keys()) if config_data else 'None'}")
        if config_data:
            model_params = config_data.get('model_parameters', {})
            LOGGER.info(f"模型参数: {model_params}")
            confidence_threshold = model_params.get('confidence_threshold', 0.5)
            LOGGER.info(f"读取到的置信度阈值: {confidence_threshold}")
        
        # 调用过滤方法
        filtered_result = self._filter_detections(result)
        
        hit = False
        # 初始化策略
        if self.strategy is None:
            self.strategy = self.reserved_args['strategy']
        # 生成多边形和计数线（使用配置数据）
        polygons = self._gen_polygons(config_data)
        if self.lines is None:
            self.lines = self._gen_lines(config_data)

        # 初始化跟踪器
        if self.tracker is None:
            self.tracker = Tracker(self.frame_interval)
            self.max_retain = self.tracker.track_buffer + 1
            LOGGER.info('Init tracker, source_id={}, alg_name={}, track_buffer={}'.format(
                self.source_id, self.alg_name, self.tracker.track_buffer))

        # 处理检测结果
        try:
            # 获取检测框
            if isinstance(filter_result, dict) and filter_result:
                rectangles = next(iter(filter_result.values()))
            else:
                rectangles = []
            
            # 额外的置信度过滤（确保传递给tracker的数据已过滤）
            if rectangles and hasattr(self, 'config_data') and self.config_data:
                # 支持从model_parameters中读取参数
                model_params = self.config_data.get('model_parameters', {})
                confidence_threshold = model_params.get('confidence_threshold', 0.5)
                
                # 也支持直接从config_data根级别读取
                if 'confidence_threshold' in self.config_data:
                    confidence_threshold = self.config_data['confidence_threshold']
                
                original_count = len(rectangles)
                filtered_rectangles = []
                for rect in rectangles:
                    confidence = rect.get('conf', rect.get('confidence', 0))
                    if confidence >= confidence_threshold:
                        filtered_rectangles.append(rect)
                rectangles = filtered_rectangles
                LOGGER.info(f"后处理置信度过滤: 阈值={confidence_threshold}, 过滤前={original_count}, 过滤后={len(rectangles)}")
            
            # 目标跟踪 - 后处理器负责生成合理的车辆ID
            tracker_result = self.tracker.track(rectangles)
            # 检查丢失目标
            self.__check_lost_target(tracker_result)
            # 处理每个跟踪目标
            processed_rectangles = []
            area_detection_count = 0  # 区域内检测计数

            LOGGER.info(f"跟踪器返回结果: {len(tracker_result)}个目标")
            for track_id, rectangle in tracker_result.items():
                LOGGER.debug(f"处理车辆ID: {track_id}, 边界框: {rectangle.get('xyxy', [])}")

                target = self.targets.get(track_id)
                if target is None:
                    target = {
                        'lost': 0,
                        'pre_target': None
                    }
                    self.targets[track_id] = target
                    LOGGER.info(f"新增跟踪目标: track_id={track_id}")

                # 每帧重新初始化告警状态，避免累积
                is_alert = False
                alert_reason = ""
                is_in_area = False  # 标记是否在区域内

                # 清除前一帧可能残留的告警信息
                if 'alert_reason' in rectangle:
                    del rectangle['alert_reason']

                # 1. 检查是否在检测区域内
                if polygons:
                    center_x = (rectangle['xyxy'][0] + rectangle['xyxy'][2]) / 2
                    center_y = (rectangle['xyxy'][1] + rectangle['xyxy'][3]) / 2

                    for area_id, area in polygons.items():
                        if self._point_in_polygon([center_x, center_y], area['polygon']):
                            is_in_area = True
                            area_detection_count += 1
                            # 注意：在区域内不一定是告警，需要根据具体算法逻辑判断
                            LOGGER.debug(f'车辆在检测区域内: track_id={track_id}, area={area_id}')
                            break
                else:
                    # 如果没有配置区域，则认为所有检测框都在"区域内"
                    is_in_area = True
                    LOGGER.debug(f"无配置区域，车辆ID {track_id} 默认在区域内")

                # 只处理在区域内的目标
                if not is_in_area:
                    LOGGER.debug(f"跳过区域外车辆: track_id={track_id}")
                    continue

                # 2. 跨线计数检测（只对区域内的目标进行）
                if target['pre_target'] is not None:
                    for line_id, line in self.lines.items():
                        count_result = self._cross_line_counting(
                            target['pre_target']['xyxy'], rectangle['xyxy'],
                            line['line'], line['ext']['direction'], self.strategy)
                        if count_result is not None:
                            is_alert = True
                            alert_reason = f"车辆跨越{line['ext']['name']}"
                            self._merge_cross_line_counting_result(line['ext']['result'], count_result)
                            LOGGER.info(f'车辆跨线计数: track_id={track_id}, direction={count_result["direction"]}, line={line_id}')
                            break

                # 更新目标的前一帧信息（用于下一帧的跨线检测）
                rectangle_copy = rectangle.copy()
                rectangle_copy['track_id'] = track_id
                target['pre_target'] = rectangle_copy

                # 设置颜色和告警状态
                if is_alert:
                    hit = True
                    rectangle['color'] = self.alert_color
                    rectangle['alert_reason'] = alert_reason
                    LOGGER.info(f"车辆ID {track_id} 触发告警: {alert_reason}")
                else:
                    rectangle['color'] = self.non_alert_color
                    # 清除可能存在的告警原因
                    if 'alert_reason' in rectangle:
                        del rectangle['alert_reason']

                # 确保track_id被正确设置到检测结果中
                rectangle['track_id'] = track_id

                # 区域内的检测框都要显示（无论是否告警）
                processed_rectangles.append(rectangle.copy())

                LOGGER.debug(f"处理完成车辆ID: {track_id}, 告警状态: {is_alert}, 在区域内: {is_in_area}, 颜色: {rectangle['color']}")
            # 构建告警消息
            alert_messages = []
            if area_detection_count > 0:
                alert_messages.append(f"区域内检测到{area_detection_count}辆车")
            total_line_count = sum(line['ext']['result']['total'] for line in self.lines.values())
            if total_line_count > 0:
                alert_messages.append(f"跨线计数{total_line_count}次")
            
            # 判断是否触发告警（根据告警阈值）
            alert_threshold = 1
            if hasattr(self, 'config_data') and self.config_data:
                alert_params = self.config_data.get('alert_parameters', {})
                alert_threshold = alert_params.get('alert_threshold', 1)
            
            total_alert_count = area_detection_count + total_line_count
            hit = total_alert_count >= alert_threshold
            
            if hit:
                message = f"车辆计数告警: {', '.join(alert_messages)} (阈值: {alert_threshold})"
            else:
                message = f"检测到 {len(processed_rectangles)} 个车辆目标，计数: {total_alert_count} (阈值: {alert_threshold})"
            
            # 提取所有车辆ID用于告警记录
            vehicle_ids = [rect.get('track_id') for rect in processed_rectangles if rect.get('track_id') is not None]
            unique_vehicle_ids = list(set(vehicle_ids))

            LOGGER.info(f"后处理完成: 检测到{len(processed_rectangles)}个目标, 车辆ID: {vehicle_ids}")
            LOGGER.info(f"唯一车辆ID: {unique_vehicle_ids}, 告警状态: {hit}")

            # 构建返回结果，使用自定义格式
            return {
                'hit': hit,
                'message': message,
                'details': {
                    'detections': processed_rectangles,
                    'lines': self.lines,
                    'polygons': polygons,
                    'area_detection_count': area_detection_count,
                    'total_line_count': total_line_count,
                    'total_alert_count': total_alert_count,
                    'alert_threshold': alert_threshold,
                    'alert_messages': alert_messages,
                    'frame_shape': [640, 480],  # 默认尺寸，实际会从frame获取
                    'timestamp': datetime.now().isoformat(),
                    # 添加车辆跟踪信息
                    'vehicle_tracking': {
                        'vehicle_ids': vehicle_ids,
                        'unique_vehicle_ids': unique_vehicle_ids,
                        'vehicle_count': len(processed_rectangles),
                        'unique_vehicle_count': len(unique_vehicle_ids)
                    }
                }
            }
        except Exception as e:
            LOGGER.error(f'车辆计数处理失败: {e}')
            return {
                'hit': False,
                'message': f'处理失败: {str(e)}',
                'details': {}
            }
