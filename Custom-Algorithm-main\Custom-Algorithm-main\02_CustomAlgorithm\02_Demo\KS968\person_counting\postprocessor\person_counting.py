import sys
import os
from datetime import datetime
sys.path.insert(0, os.path.dirname(__file__))
from logger import LOGGER
from tracker import Tracker
class Postprocessor:
    def __init__(self, source_id=None, alg_name=None):
        # PyTorch后处理器，不需要基类
        self.source_id = source_id
        self.alg_name = alg_name
        self.strategy = None
        self.tracker = None
        self.max_retain = 0
        self.targets = {}
        self.lines = None

        # 智驱力标准属性
        self.reserved_args = {
            'strategy': 'center',
            'ch_name': '人员计数',
            'sound_text': '人员计数告警'
        }
        self.frame_interval = 1
        self.alert_color = [255, 0, 0]  # 红色
        self.non_alert_color = [0, 255, 0]  # 绿色

    def __check_lost_target(self, tracker_result):
        for track_id in list(self.targets.keys()):
            if track_id not in tracker_result:
                self.targets[track_id]['lost'] += 1
            else:
                self.targets[track_id]['lost'] = 0
            if self.targets[track_id]['lost'] > self.max_retain:
                LOGGER.info('Target lost, source_id={}, alg_name={}, track_id={}, pre_target={}'.format(
                    self.source_id, self.alg_name, track_id, self.targets[track_id]['pre_target']))
                del self.targets[track_id]
        return True

    def _gen_polygons(self, config_data=None):
        """生成多边形区域"""
        polygons = {}

        if config_data:
            # 获取图像尺寸
            image_width = config_data.get('image_width', 640)
            image_height = config_data.get('image_height', 480)

            # 从配置数据中获取bbox配置
            bbox_config = config_data.get('bbox_config', {})
            polygon_configs = bbox_config.get('polygons', [])

            LOGGER.info(f"从配置中读取到 {len(polygon_configs)} 个多边形区域")

            for i, polygon_config in enumerate(polygon_configs):
                polygon_id = f"polygon_{i}"

                # 获取多边形点坐标
                points = polygon_config.get('points', [])
                if len(points) >= 3:  # 至少需要3个点构成多边形
                    # 转换相对坐标为绝对坐标
                    absolute_points = []
                    for point in points:
                        if isinstance(point, list) and len(point) >= 2:
                            # 如果坐标值在0-1之间，认为是相对坐标
                            if 0 <= point[0] <= 1 and 0 <= point[1] <= 1:
                                abs_x = int(point[0] * image_width)
                                abs_y = int(point[1] * image_height)
                            else:
                                abs_x = int(point[0])
                                abs_y = int(point[1])
                            absolute_points.append([abs_x, abs_y])

                    if len(absolute_points) >= 3:
                        polygons[polygon_id] = {
                            'polygon': absolute_points,
                            'name': polygon_config.get('name', f'检测区域{i+1}'),
                            'color': polygon_config.get('color', [0, 255, 0]),
                            'thickness': polygon_config.get('thickness', 2)
                        }

        LOGGER.info(f"总共生成 {len(polygons)} 个检测区域")
        return polygons

    def _gen_lines(self, config_data=None):
        """生成计数线"""
        lines = {}
        if config_data:
            # 获取图像尺寸
            image_width = config_data.get('image_width', 640)
            image_height = config_data.get('image_height', 480)

            # 从配置数据中获取bbox配置
            bbox_config = config_data.get('bbox_config', {})
            line_configs = bbox_config.get('lines', [])

            LOGGER.info(f"从配置中读取到 {len(line_configs)} 条计数线")

            for i, line_config in enumerate(line_configs):
                line_id = f"line_{i}"

                # 获取线段点坐标
                points = line_config.get('points', [])
                if len(points) >= 2:  # 至少需要2个点构成线段
                    # 转换相对坐标为绝对坐标
                    line_points = []
                    for point in points:
                        if isinstance(point, list) and len(point) >= 2:
                            # 如果坐标值在0-1之间，认为是相对坐标
                            if 0 <= point[0] <= 1 and 0 <= point[1] <= 1:
                                abs_x = int(point[0] * image_width)
                                abs_y = int(point[1] * image_height)
                            else:
                                abs_x = int(point[0])
                                abs_y = int(point[1])
                            line_points.append([abs_x, abs_y])

                    if len(line_points) >= 2:
                        lines[line_id] = {
                            'line': line_points,
                            'ext': {
                                'name': line_config.get('name', f'计数线{i+1}'),
                                'direction': line_config.get('direction', 'both'),
                                'result': {'up': 0, 'down': 0, 'total': 0}
                            }
                        }
                        LOGGER.info(f"生成计数线{line_id}: {len(line_points)}个点")

        LOGGER.info(f"总共生成 {len(lines)} 条计数线")
        return lines

    def _point_in_polygon(self, point, polygon):
        """判断点是否在多边形内（射线法）"""
        x, y = point
        n = len(polygon)
        inside = False
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        return inside

    def _cross_line_counting(self, pre_bbox, cur_bbox, line, direction, strategy):
        """跨线计数逻辑"""
        # 获取目标中心点
        pre_center = [(pre_bbox[0] + pre_bbox[2]) / 2, (pre_bbox[1] + pre_bbox[3]) / 2]
        cur_center = [(cur_bbox[0] + cur_bbox[2]) / 2, (cur_bbox[1] + cur_bbox[3]) / 2]
        # 检查是否跨越计数线
        line_y = line[0][1]
        if pre_center[1] < line_y and cur_center[1] > line_y:
            # 从上往下跨线
            return {'direction': 'down', 'count': 1}
        elif pre_center[1] > line_y and cur_center[1] < line_y:
            # 从下往上跨线
            return {'direction': 'up', 'count': 1}
        return None

    def _merge_cross_line_counting_result(self, line_result, count_result):
        """合并跨线计数结果"""
        direction = count_result['direction']
        count = count_result['count']
        if direction in line_result:
            line_result[direction] += count
        else:
            line_result[direction] = count
        line_result['total'] = line_result.get('up', 0) + line_result.get('down', 0)
        LOGGER.info(f"跨线计数更新: {direction}方向+{count}, 总计数={line_result['total']}")

    def _filter_detections(self, result: Dict) -> Dict:
        """过滤检测结果，支持置信度过滤"""
        # 默认置信度阈值
        confidence_threshold = 0.5
        nms_threshold = 0.5

        # 从配置数据中获取阈值
        if hasattr(self, 'config_data') and self.config_data:
            model_params = self.config_data.get('model_parameters', {})
            confidence_threshold = model_params.get('confidence_threshold', confidence_threshold)
            nms_threshold = model_params.get('nms_threshold', nms_threshold)

        LOGGER.info(f"使用置信度阈值: {confidence_threshold}, NMS阈值: {nms_threshold}")

        filtered_result = {}

        for model_name, detections in result.items():
            filtered_detections = []

            for detection in detections:
                # 置信度过滤
                confidence = detection.get('conf', detection.get('confidence', 0))
                if confidence >= confidence_threshold:
                    filtered_detections.append(detection)

            filtered_result[model_name] = filtered_detections
            LOGGER.info(f"模型 {model_name}: 原始检测数={len(detections)}, 过滤后={len(filtered_detections)}")

        return filtered_result

    def _process(self, result, filter_result, config_data=None):
        """处理检测结果 - 参考车辆计数逻辑"""
        # 保存配置数据到实例变量，供_filter_detections使用
        self.config_data = config_data

        # 首先进行置信度过滤
        LOGGER.info(f"开始人员计数后处理，配置数据: {list(config_data.keys()) if config_data else 'None'}")
        if config_data:
            model_params = config_data.get('model_parameters', {})
            LOGGER.info(f"模型参数: {model_params}")
            confidence_threshold = model_params.get('confidence_threshold', 0.5)
            LOGGER.info(f"读取到的置信度阈值: {confidence_threshold}")

        # 调用过滤方法
        filtered_result = self._filter_detections(result)

        hit = False
        # 初始化策略
        if self.strategy is None:
            self.strategy = self.reserved_args['strategy']
        # 生成多边形和计数线（使用配置数据）
        polygons = self._gen_polygons(config_data)
        if self.lines is None:
            self.lines = self._gen_lines(config_data)

        # 初始化跟踪器
        if self.tracker is None:
            self.tracker = Tracker(self.frame_interval)
            self.max_retain = self.tracker.track_buffer + 1
            LOGGER.info('Init tracker, source_id={}, alg_name={}, track_buffer={}'.format(
                self.source_id, self.alg_name, self.tracker.track_buffer))

        try:
            # 获取过滤后的检测结果
            model_name, rectangles = next(iter(filtered_result.items()))
            LOGGER.info(f"人员计数处理: 模型={model_name}, 检测框数量={len(rectangles)}")

            # 收集人员ID信息
            person_ids = []
            unique_person_ids = set()

            # 目标跟踪 - 后处理器负责生成合理的人员ID
            tracker_result = self.tracker.track(rectangles)
            # 检查丢失目标
            self.__check_lost_target(tracker_result)
            # 处理每个跟踪目标
            processed_rectangles = []
            area_detection_count = 0  # 区域内检测计数

            LOGGER.info(f"跟踪器返回结果: {len(tracker_result)}个目标")
            for track_id, rectangle in tracker_result.items():
                LOGGER.debug(f"处理人员ID: {track_id}, 边界框: {rectangle.get('xyxy', [])}")

                target = self.targets.get(track_id)
                if target is None:
                    target = {
                        'lost': 0,
                        'pre_target': None
                    }
                    self.targets[track_id] = target
                    LOGGER.info(f"新增跟踪目标: track_id={track_id}")

                is_alert = False
                alert_reason = ""
                # 1. 检查是否在检测区域内
                if polygons:
                    center_x = (rectangle['xyxy'][0] + rectangle['xyxy'][2]) / 2
                    center_y = (rectangle['xyxy'][1] + rectangle['xyxy'][3]) / 2

                    for area_id, area in polygons.items():
                        if self._point_in_polygon([center_x, center_y], area['polygon']):
                            is_alert = True
                            area_detection_count += 1
                            alert_reason = f"人员进入{area['name']}"
                            LOGGER.info(f'人员进入检测区域: track_id={track_id}, area={area_id}')
                            break
                # 2. 跨线计数检测
                if target['pre_target'] is not None:
                    for line_id, line in self.lines.items():
                        count_result = self._cross_line_counting(
                            target['pre_target']['xyxy'], rectangle['xyxy'],
                            line['line'], line['ext']['direction'], self.strategy)
                        if count_result is not None:
                            is_alert = True
                            alert_reason = f"人员跨越{line['ext']['name']}"
                            self._merge_cross_line_counting_result(line['ext']['result'], count_result)
                            LOGGER.info(f'人员跨线计数: track_id={track_id}, direction={count_result["direction"]}, line={line_id}')
                            break
                # 设置颜色和告警状态
                if is_alert:
                    hit = True
                    rectangle['color'] = self.alert_color
                    rectangle['alert_reason'] = alert_reason
                    LOGGER.info(f"人员ID {track_id} 触发告警: {alert_reason}")
                else:
                    rectangle['color'] = self.non_alert_color

                # 确保track_id被正确设置到检测结果中
                rectangle['track_id'] = track_id
                target['pre_target'] = rectangle.copy()  # 使用copy避免引用问题
                processed_rectangles.append(rectangle.copy())  # 使用copy避免重复引用

                LOGGER.debug(f"处理完成人员ID: {track_id}, 告警状态: {is_alert}")

            # 构建告警消息
            alert_messages = []
            if area_detection_count > 0:
                alert_messages.append(f"区域内检测到{area_detection_count}个人员")
            total_line_count = sum(line['ext']['result']['total'] for line in self.lines.values())
            if total_line_count > 0:
                alert_messages.append(f"跨线计数{total_line_count}次")

            # 判断是否触发告警（根据告警阈值）
            alert_threshold = 1
            if hasattr(self, 'config_data') and self.config_data:
                alert_params = self.config_data.get('alert_parameters', {})
                alert_threshold = alert_params.get('alert_threshold', 1)

            total_alert_count = area_detection_count + total_line_count
            hit = total_alert_count >= alert_threshold

            if hit:
                message = f"人员计数告警: {', '.join(alert_messages)} (阈值: {alert_threshold})"
            else:
                message = f"检测到 {len(processed_rectangles)} 个人员目标，计数: {total_alert_count} (阈值: {alert_threshold})"

            LOGGER.info(f"人员计数处理完成: hit={hit}, message={message}")

            # 构建返回结果，使用自定义格式
            return {
                'hit': hit,
                'message': message,
                'details': {
                    'detections': processed_rectangles,
                    'lines': self.lines,
                    'polygons': polygons,
                    'area_detection_count': area_detection_count,
                    'total_line_count': total_line_count,
                    'total_alert_count': total_alert_count,
                    'alert_threshold': alert_threshold,
                    'alert_messages': alert_messages,
                    'frame_shape': [640, 480],  # 默认尺寸，实际会从frame获取
                    'timestamp': datetime.now().isoformat(),
                    # 添加人员跟踪信息
                    'person_tracking': {
                        'person_ids': person_ids,
                        'unique_person_ids': unique_person_ids,
                        'person_count': len(processed_rectangles),
                        'unique_person_count': len(unique_person_ids)
                    }
                }
            }
        except Exception as e:
            LOGGER.error(f'人员计数处理失败: {e}')
            return {
                'hit': False,
                'message': f'处理失败: {str(e)}',
                'details': {}
            }
