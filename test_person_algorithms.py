#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试人员计数和人员区域入侵算法包
"""

import sys
import os
import cv2
import numpy as np
from datetime import datetime

# 添加算法包路径
person_counting_path = "Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/person_counting"
person_intrusion_path = "Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/person_intrusion"

def test_person_counting():
    """测试人员计数算法包"""
    print("=" * 50)
    print("测试人员计数算法包")
    print("=" * 50)

    try:
        # 临时添加人员计数路径
        sys.path.insert(0, person_counting_path)

        # 导入模型和后处理器
        from model.model import Model
        from postprocessor.person_counting import Postprocessor
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        print(f"创建测试图像: {test_image.shape}")
        
        # 初始化模型
        print("初始化人员计数模型...")
        model = Model()
        print(f"模型状态: {model.status}")
        
        # 初始化后处理器
        print("初始化人员计数后处理器...")
        postprocessor = Postprocessor(source_id="test", alg_name="person_counting")
        
        # 创建配置数据
        config_data = {
            'image_width': 640,
            'image_height': 480,
            'model_parameters': {
                'confidence_threshold': 0.5,
                'nms_threshold': 0.5
            },
            'alert_parameters': {
                'alert_threshold': 1
            },
            'bbox_config': {
                'polygons': [
                    {
                        'name': '检测区域1',
                        'points': [[0.2, 0.2], [0.8, 0.2], [0.8, 0.8], [0.2, 0.8]],
                        'color': [0, 255, 0]
                    }
                ],
                'lines': [
                    {
                        'name': '计数线1',
                        'points': [[0.1, 0.5], [0.9, 0.5]],
                        'direction': 'both'
                    }
                ]
            }
        }
        
        # 模拟推理结果
        mock_result = {
            'model': [
                {
                    'xyxy': [100, 100, 200, 300],
                    'conf': 0.8,
                    'cls': 0,
                    'name': 'person'
                },
                {
                    'xyxy': [300, 150, 400, 350],
                    'conf': 0.7,
                    'cls': 0,
                    'name': 'person'
                }
            ]
        }
        
        # 测试后处理
        print("测试人员计数后处理...")
        result = postprocessor._process(mock_result, mock_result, config_data)
        
        print(f"后处理结果:")
        print(f"  - 命中: {result['hit']}")
        print(f"  - 消息: {result['message']}")
        print(f"  - 检测数量: {len(result['details']['detections'])}")
        print(f"  - 区域检测计数: {result['details']['area_detection_count']}")
        print(f"  - 跨线计数: {result['details']['total_line_count']}")
        
        print("✅ 人员计数算法包测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 人员计数算法包测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_person_intrusion():
    """测试人员区域入侵算法包"""
    print("=" * 50)
    print("测试人员区域入侵算法包")
    print("=" * 50)
    
    try:
        # 导入模型和后处理器
        from model.model import Model
        from postprocessor.person_intrusion import Postprocessor
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        print(f"创建测试图像: {test_image.shape}")
        
        # 初始化模型
        print("初始化人员区域入侵模型...")
        model = Model()
        print(f"模型状态: {model.status}")
        
        # 初始化后处理器
        print("初始化人员区域入侵后处理器...")
        postprocessor = Postprocessor(source_id="test", alg_name="person_intrusion")
        
        # 创建配置数据
        config_data = {
            'image_width': 640,
            'image_height': 480,
            'model_parameters': {
                'confidence_threshold': 0.5,
                'nms_threshold': 0.5
            },
            'alert_parameters': {
                'alert_threshold': 1
            },
            'bbox_config': {
                'polygons': [
                    {
                        'name': '入侵区域1',
                        'points': [[0.3, 0.3], [0.7, 0.3], [0.7, 0.7], [0.3, 0.7]],
                        'color': [255, 0, 0]
                    }
                ]
            }
        }
        
        # 模拟推理结果
        mock_result = {
            'model': [
                {
                    'xyxy': [320, 240, 420, 440],  # 在入侵区域内
                    'conf': 0.9,
                    'cls': 0,
                    'name': 'person'
                },
                {
                    'xyxy': [50, 50, 150, 250],   # 在入侵区域外
                    'conf': 0.6,
                    'cls': 0,
                    'name': 'person'
                }
            ]
        }
        
        # 测试后处理
        print("测试人员区域入侵后处理...")
        result = postprocessor._process(mock_result, mock_result, config_data)
        
        print(f"后处理结果:")
        print(f"  - 命中: {result['hit']}")
        print(f"  - 消息: {result['message']}")
        print(f"  - 检测数量: {len(result['details']['detections'])}")
        print(f"  - 入侵计数: {result['details']['intrusion_count']}")
        
        print("✅ 人员区域入侵算法包测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 人员区域入侵算法包测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print(f"开始测试人员算法包 - {datetime.now()}")
    
    # 测试人员计数
    counting_success = test_person_counting()
    
    print("\n")
    
    # 测试人员区域入侵
    intrusion_success = test_person_intrusion()
    
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"人员计数算法包: {'✅ 通过' if counting_success else '❌ 失败'}")
    print(f"人员区域入侵算法包: {'✅ 通过' if intrusion_success else '❌ 失败'}")
    
    if counting_success and intrusion_success:
        print("🎉 所有算法包测试通过！")
        return True
    else:
        print("⚠️ 部分算法包测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
